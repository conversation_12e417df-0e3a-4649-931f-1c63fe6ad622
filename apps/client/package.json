{"name": "bizzu-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint ./src", "prettier:fix": "prettier ./src --write", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.10", "@tanstack/react-router": "^1.121.24", "i18next": "^25.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-i18next": "^15.5.3", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.25.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@stylistic/eslint-plugin": "^5.2.2", "@tanstack/eslint-plugin-query": "^5.78.0", "@tanstack/router-plugin": "^1.121.25", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "sass-embedded": "^1.89.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "engines": {"node": ">=22.16.0 < 23", "npm": ">=10.9.2 < 11"}}