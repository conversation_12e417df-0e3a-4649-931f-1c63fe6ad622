import { StrictMode } from "react";
import { Theme } from "@radix-ui/themes";
// we are not importing all styles to reduce css bundle size
// check https://www.radix-ui.com/themes/docs/theme/color#individual-css-files
import "@radix-ui/themes/components.css";
import "@radix-ui/themes/tokens/base.css";
import "@radix-ui/themes/utilities.css";
import "@radix-ui/themes/tokens/colors/red.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { createRoot } from "react-dom/client";
import { queryClient } from "@/utils/queryClient";
import App from "./app";
import "./utils/i18n";
import "./index.css";

createRoot(document.getElementById("root")!).render(
    <StrictMode>
        {/* colors are defined in index.css */}
        <Theme accentColor="purple" grayColor="gray" radius="large" scaling="95%" hasBackground={false}>
            <QueryClientProvider client={queryClient}>
                <App />
            </QueryClientProvider>
        </Theme>
    </StrictMode>,
);
