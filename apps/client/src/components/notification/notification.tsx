import type React from "react";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Callout } from "@radix-ui/themes";

type NotificationType = "error" | "success" | "info";

type NotificationProps = {
    type: NotificationType;
    children: React.ReactNode;
};

type Color = "red" | "green" | "blue";

export default function Notification({ type, children }: NotificationProps) {
    const color = {
        error: "red",
        success: "green",
        info: "blue",
    }[type] as Color;

    return (
        <Callout.Root color={color}>
            <Callout.Icon>
                <InfoCircledIcon />
            </Callout.Icon>
            <Callout.Text>{children}</Callout.Text>
        </Callout.Root>
    );
}
