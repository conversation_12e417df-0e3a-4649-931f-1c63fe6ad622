import { useState } from "react";
import { Box, Flex } from "@radix-ui/themes";
import { ExpandCollapseButton } from "./expandCollapseButton";
import { Navigation } from "./navigation";

export function Sidebar() {
    const [isExpanded, setIsExpanded] = useState(false);

    const handleToggle = () => {
        setIsExpanded((expanded) => !expanded);
    };

    return (
        <Flex height="100%" justify="between" direction="column">
            <Box width="100%">
                <Navigation isExpanded={isExpanded} />
            </Box>
            <Flex width="100%">
                <ExpandCollapseButton isExpanded={isExpanded} onToggle={handleToggle} />
            </Flex>
        </Flex>
    );
}
