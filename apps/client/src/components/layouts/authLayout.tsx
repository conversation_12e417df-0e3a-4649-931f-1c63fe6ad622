import { <PERSON>, Card, Flex, <PERSON><PERSON>, <PERSON> } from "@/components";
import { Footer } from "@/components/footer";
import { BizzuLogo } from "@/components/logo/bizzuLogo";
import styles from "./authLayout.module.scss";

export function AuthLayout({
    title,
    subtitle,
    children,
}: {
    title: string;
    subtitle: string;
    children: React.ReactNode;
}) {
    return (
        <>
            <Box maxWidth={"450px"} width={"100%"}>
                <Box mb="9">
                    <BizzuLogo />
                </Box>
                <Card className={styles.card}>
                    <Box p="4">
                        <Box mb="6">
                            <Heading as="h2" size="6" align="center">
                                <Strong>{title}</Strong>
                            </Heading>
                            <Heading as="h3" size="2" align="center" color="gray">
                                {subtitle}
                            </Heading>
                        </Box>
                        {children}
                    </Box>
                </Card>
                <Flex mt="6" justify="center">
                    <Footer />
                </Flex>
            </Box>
        </>
    );
}
