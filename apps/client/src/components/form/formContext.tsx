import { createContext, useContext } from "react";
import type { Control, FieldValues } from "react-hook-form";

export const FormContext = createContext<{
    // eslint-disable-next-line  @typescript-eslint/no-explicit-any
    control: Control<any>;
    // eslint-disable-next-line  @typescript-eslint/no-explicit-any
    errors: Record<string, any>;
} | null>(null);

export const useFormContext = <T extends FieldValues>() => {
    const context = useContext(FormContext);

    if (!context) {
        throw new Error("useFormContext must be used inside <FormProvider>");
    }

    return context as {
        control: Control<T>;
        // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        errors: Record<keyof T, any>;
    };
};
