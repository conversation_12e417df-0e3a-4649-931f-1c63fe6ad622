import { RadioGroup as RadixRadioGroup, Text } from "@radix-ui/themes";
import { Controller } from "react-hook-form";
import type { FieldPathValue, FieldValues, Path } from "react-hook-form";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

type Option<T> = {
    label: string;
    value: T;
};

type RadioGroupProps<T extends FieldValues> = {
    [K in Path<T>]: {
        name: K;
        label: string;
        options: Option<FieldPathValue<T, K>>[];
    } & Omit<RadixRadioGroup.RootProps, "value" | "onValueChange">;
}[Path<T>];

export function RadioGroup<T extends FieldValues>({ name, label, options, ...rest }: RadioGroupProps<T>) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label={label} error={fieldState.error?.message}>
                    <RadixRadioGroup.Root {...rest} value={field.value} onValueChange={field.onChange}>
                        {options.map((opt) => (
                            <RadixRadioGroup.Item key={opt.value} value={opt.value}>
                                <Text as="label">{opt.label}</Text>
                            </RadixRadioGroup.Item>
                        ))}
                    </RadixRadioGroup.Root>
                </FormField>
            )}
        />
    );
}
