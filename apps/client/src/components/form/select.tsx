import { Select as RadixSelect } from "@radix-ui/themes";
import { Controller } from "react-hook-form";
import type { FieldPathValue, FieldValues, Path } from "react-hook-form";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

type Option<T> = {
    label: string;
    value: T;
};

type SelectFieldProps<T extends FieldValues> = {
    [K in Path<T>]: {
        name: K;
        label: string;
        options: Option<FieldPathValue<T, K>>[];
    } & Omit<RadixSelect.RootProps, "value" | "onValueChange">;
}[Path<T>];

export function Select<T extends FieldValues>({ name, label, options, ...rest }: SelectFieldProps<T>) {
    const { control } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label={label} error={fieldState.error?.message}>
                    <RadixSelect.Root value={field.value} onValueChange={field.onChange}>
                        <RadixSelect.Trigger {...rest} />
                        <RadixSelect.Content>
                            {options.map((opt) => (
                                <RadixSelect.Item key={opt.value} value={opt.value}>
                                    {opt.label}
                                </RadixSelect.Item>
                            ))}
                        </RadixSelect.Content>
                    </RadixSelect.Root>
                </FormField>
            )}
        />
    );
}
