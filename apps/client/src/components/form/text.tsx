import { TextField } from "@radix-ui/themes";
import { Controller } from "react-hook-form";
import type { FieldValues, Path } from "react-hook-form";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

export type InputFieldProps<T extends FieldValues> = {
    name: Path<T>;
    label: string;
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
} & TextField.RootProps;

export function Text<T extends FieldValues>({
    name,
    label,
    startAdornment,
    endAdornment,
    ...rest
}: InputFieldProps<T>) {
    const { control, errors } = useFormContext<T>();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FormField label={label} error={errors[name]?.message}>
                    <TextField.Root {...rest} {...field}>
                        {startAdornment && <TextField.Slot side="left">{startAdornment}</TextField.Slot>}
                        {endAdornment && <TextField.Slot side="right">{endAdornment}</TextField.Slot>}
                    </TextField.Root>
                </FormField>
            )}
        />
    );
}
