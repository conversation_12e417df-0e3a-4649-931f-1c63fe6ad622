import type { ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form<PERSON>rovider as RHFProvider, useForm } from "react-hook-form";
import type { SubmitHand<PERSON> } from "react-hook-form";
import { z } from "zod/v4";
import { FormContext } from "./formContext";

type FormProps<TSchema extends z.ZodSchema> = {
    schema: TSchema;
    onSubmit: SubmitHandler<z.infer<TSchema>>;
    children: ReactNode;
    defaultValues?: z.infer<TSchema>;
};

export function Form<T extends z.ZodSchema>({ children, schema, defaultValues, onSubmit }: FormProps<T>) {
    const methods = useForm<T>({
        // @ts-expect-error some zod v4 problem
        resolver: zodResolver(schema),
        // @ts-expect-error some zod v4 problem
        defaultValues,
    });

    return (
        <RHFProvider {...methods}>
            <FormContext.Provider
                value={{
                    control: methods.control,
                    errors: methods.formState.errors,
                }}
            >
                <form
                    onSubmit={
                        // @ts-expect-error some zod v4 problem
                        methods.handleSubmit(onSubmit)
                    }
                >
                    {children}
                </form>
            </FormContext.Provider>
        </RHFProvider>
    );
}
