import { useState } from "react";
import { EyeClosedIcon, EyeOpenIcon } from "@radix-ui/react-icons";
import type { FieldValues } from "react-hook-form";
import { Text, type InputFieldProps } from "./text";

type PasswordFieldProps<T extends FieldValues> = Omit<InputFieldProps<T>, "endAdornment" | "password">;

export function Password<T extends FieldValues>({ name, label, startAdornment, ...rest }: PasswordFieldProps<T>) {
    const [showPassword, setShowPassword] = useState(false);

    const toggleShowPassword = () => setShowPassword((prev) => !prev);
    const endAdornment = showPassword ? (
        <EyeOpenIcon onClick={toggleShowPassword} />
    ) : (
        <EyeClosedIcon onClick={toggleShowPassword} />
    );
    const inputType = showPassword ? "text" : "password";

    return (
        <Text
            {...rest}
            name={name}
            label={label}
            startAdornment={startAdornment}
            endAdornment={endAdornment}
            type={inputType}
        />
    );
}
