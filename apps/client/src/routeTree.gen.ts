/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as PublicRouteImport } from './routes/_public'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as PublicPasswordResetRouteImport } from './routes/_public/password-reset'
import { Route as PublicLoginRouteImport } from './routes/_public/login'
import { Route as PublicForgotPasswordRouteImport } from './routes/_public/forgot-password'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings'

const PublicRoute = PublicRouteImport.update({
  id: '/_public',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const PublicPasswordResetRoute = PublicPasswordResetRouteImport.update({
  id: '/password-reset',
  path: '/password-reset',
  getParentRoute: () => PublicRoute,
} as any)
const PublicLoginRoute = PublicLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => PublicRoute,
} as any)
const PublicForgotPasswordRoute = PublicForgotPasswordRouteImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => PublicRoute,
} as any)
const AuthenticatedSettingsRoute = AuthenticatedSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => AuthenticatedRoute,
} as any)

export interface FileRoutesByFullPath {
  '/settings': typeof AuthenticatedSettingsRoute
  '/forgot-password': typeof PublicForgotPasswordRoute
  '/login': typeof PublicLoginRoute
  '/password-reset': typeof PublicPasswordResetRoute
  '/': typeof AuthenticatedIndexRoute
}
export interface FileRoutesByTo {
  '/settings': typeof AuthenticatedSettingsRoute
  '/forgot-password': typeof PublicForgotPasswordRoute
  '/login': typeof PublicLoginRoute
  '/password-reset': typeof PublicPasswordResetRoute
  '/': typeof AuthenticatedIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/_public': typeof PublicRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRoute
  '/_public/forgot-password': typeof PublicForgotPasswordRoute
  '/_public/login': typeof PublicLoginRoute
  '/_public/password-reset': typeof PublicPasswordResetRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/settings'
    | '/forgot-password'
    | '/login'
    | '/password-reset'
    | '/'
  fileRoutesByTo: FileRoutesByTo
  to: '/settings' | '/forgot-password' | '/login' | '/password-reset' | '/'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_public'
    | '/_authenticated/settings'
    | '/_public/forgot-password'
    | '/_public/login'
    | '/_public/password-reset'
    | '/_authenticated/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  PublicRoute: typeof PublicRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_public': {
      id: '/_public'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PublicRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_public/password-reset': {
      id: '/_public/password-reset'
      path: '/password-reset'
      fullPath: '/password-reset'
      preLoaderRoute: typeof PublicPasswordResetRouteImport
      parentRoute: typeof PublicRoute
    }
    '/_public/login': {
      id: '/_public/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof PublicLoginRouteImport
      parentRoute: typeof PublicRoute
    }
    '/_public/forgot-password': {
      id: '/_public/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof PublicForgotPasswordRouteImport
      parentRoute: typeof PublicRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedSettingsRoute: typeof AuthenticatedSettingsRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedSettingsRoute: AuthenticatedSettingsRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

interface PublicRouteChildren {
  PublicForgotPasswordRoute: typeof PublicForgotPasswordRoute
  PublicLoginRoute: typeof PublicLoginRoute
  PublicPasswordResetRoute: typeof PublicPasswordResetRoute
}

const PublicRouteChildren: PublicRouteChildren = {
  PublicForgotPasswordRoute: PublicForgotPasswordRoute,
  PublicLoginRoute: PublicLoginRoute,
  PublicPasswordResetRoute: PublicPasswordResetRoute,
}

const PublicRouteWithChildren =
  PublicRoute._addFileChildren(PublicRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  PublicRoute: PublicRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
