@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap");

body,
html {
    margin: 0;
    padding: 0;
}

#root {
    background: linear-gradient(to bottom, #fffbff, #fef6ff);
}

.radix-themes {
    --default-font-family: "Roboto", sans-serif;
}

/* generated with https://www.radix-ui.com/colors/custom */
:root,
.light,
.light-theme {
    --purple-1: #fdfcff;
    --purple-2: #fbf7ff;
    --purple-3: #f5edff;
    --purple-4: #efe1ff;
    --purple-5: #e7d3ff;
    --purple-6: #ddc2ff;
    --purple-7: #cfabff;
    --purple-8: #be8bff;
    --purple-9: #9333ea;
    --purple-10: #8427d5;
    --purple-11: #8529d6;
    --purple-12: #440f71;

    --purple-a1: #5500ff03;
    --purple-a2: #8000ff08;
    --purple-a3: #7200ff12;
    --purple-a4: #7700ff1e;
    --purple-a5: #7400ff2c;
    --purple-a6: #7100ff3d;
    --purple-a7: #6e00ff54;
    --purple-a8: #7100ff74;
    --purple-a9: #7800e5cc;
    --purple-a10: #6e00ced8;
    --purple-a11: #6e00ced6;
    --purple-a12: #380068f0;

    --purple-contrast: #fff;
    --purple-surface: #faf5ffcc;
    --purple-indicator: #9333ea;
    --purple-track: #9333ea;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        :root,
        .light,
        .light-theme {
            --purple-1: oklch(99.3% 0.0053 302.3);
            --purple-2: oklch(98.2% 0.0126 302.3);
            --purple-3: oklch(95.9% 0.0335 302.3);
            --purple-4: oklch(93.3% 0.0535 302.3);
            --purple-5: oklch(90.1% 0.0744 302.3);
            --purple-6: oklch(86% 0.0974 302.3);
            --purple-7: oklch(80.4% 0.128 302.3);
            --purple-8: oklch(73.3% 0.1693 302.3);
            --purple-9: oklch(55.8% 0.2525 302.3);
            --purple-10: oklch(51.2% 0.24 302.3);
            --purple-11: oklch(51.6% 0.24 302.3);
            --purple-12: oklch(32.2% 0.1518 302.3);

            --purple-a1: color(display-p3 0.349 0.0235 1 / 0.012);
            --purple-a2: color(display-p3 0.3882 0.0196 0.8784 / 0.032);
            --purple-a3: color(display-p3 0.3961 0.0078 0.9451 / 0.071);
            --purple-a4: color(display-p3 0.4039 0.0039 0.9373 / 0.118);
            --purple-a5: color(display-p3 0.3765 0.0039 0.9333 / 0.169);
            --purple-a6: color(display-p3 0.3686 0.0039 0.9373 / 0.236);
            --purple-a7: color(display-p3 0.3529 0.0039 0.9294 / 0.326);
            --purple-a8: color(display-p3 0.3608 0.0039 0.9373 / 0.448);
            --purple-a9: color(display-p3 0.3961 0 0.8471 / 0.773);
            --purple-a10: color(display-p3 0.3647 0 0.7608 / 0.82);
            --purple-a11: color(display-p3 0.3608 0 0.7647 / 0.812);
            --purple-a12: color(display-p3 0.1804 0 0.3804 / 0.926);

            --purple-contrast: #fff;
            --purple-surface: color(display-p3 0.9765 0.9608 1 / 0.8);
            --purple-indicator: oklch(55.8% 0.2525 302.3);
            --purple-track: oklch(55.8% 0.2525 302.3);
        }
    }
}

:root,
.light,
.light-theme {
    --gray-1: #fefdfd;
    --gray-2: #faf9f9;
    --gray-3: #f2efef;
    --gray-4: #ebe7e7;
    --gray-5: #e4e0e0;
    --gray-6: #ddd8d8;
    --gray-7: #d4cccc;
    --gray-8: #c2b8b8;
    --gray-9: #958989;
    --gray-10: #897f7f;
    --gray-11: #686060;
    --gray-12: #251e1e;

    --gray-a1: #80000002;
    --gray-a2: #2b000006;
    --gray-a3: #30000010;
    --gray-a4: #2b000018;
    --gray-a5: #2100001f;
    --gray-a6: #21000027;
    --gray-a7: #28000033;
    --gray-a8: #24000047;
    --gray-a9: #1a000076;
    --gray-a10: #14000080;
    --gray-a11: #0d00009f;
    --gray-a12: #080000e1;

    --gray-contrast: #ffffff;
    --gray-surface: #ffffffcc;
    --gray-indicator: #958989;
    --gray-track: #958989;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        :root,
        .light,
        .light-theme {
            --gray-1: oklch(99.4% 0.0014 17.51);
            --gray-2: oklch(98.2% 0.0019 17.51);
            --gray-3: oklch(95.6% 0.003 17.51);
            --gray-4: oklch(93.2% 0.0044 17.51);
            --gray-5: oklch(91% 0.005 17.51);
            --gray-6: oklch(88.6% 0.0065 17.51);
            --gray-7: oklch(85.3% 0.0081 17.51);
            --gray-8: oklch(79.2% 0.0109 17.51);
            --gray-9: oklch(64.1% 0.0135 17.51);
            --gray-10: oklch(60.6% 0.0126 17.51);
            --gray-11: oklch(49.8% 0.0103 17.51);
            --gray-12: oklch(24.3% 0.0104 17.51);

            --gray-a1: color(display-p3 0.5098 0.0196 0.0196 / 0.008);
            --gray-a2: color(display-p3 0.1843 0.0235 0.0235 / 0.024);
            --gray-a3: color(display-p3 0.1294 0.0078 0.0078 / 0.063);
            --gray-a4: color(display-p3 0.1333 0.0118 0.0118 / 0.095);
            --gray-a5: color(display-p3 0.102 0.0039 0.0039 / 0.122);
            --gray-a6: color(display-p3 0.1059 0.0039 0.0039 / 0.153);
            --gray-a7: color(display-p3 0.1373 0 0 / 0.2);
            --gray-a8: color(display-p3 0.1176 0.0039 0.0039 / 0.279);
            --gray-a9: color(display-p3 0.0863 0.0039 0.0039 / 0.463);
            --gray-a10: color(display-p3 0.0627 0 0 / 0.502);
            --gray-a11: color(display-p3 0.0471 0 0 / 0.624);
            --gray-a12: color(display-p3 0.0275 0 0 / 0.883);

            --gray-contrast: #ffffff;
            --gray-surface: color(display-p3 1 1 1 / 80%);
            --gray-indicator: oklch(64.1% 0.0135 17.51);
            --gray-track: oklch(64.1% 0.0135 17.51);
        }
    }
}

:root,
.light,
.light-theme,
.radix-themes {
    --color-background: #fff;
}

.dark,
.dark-theme {
    --purple-1: #130d20;
    --purple-2: #1a132b;
    --purple-3: #2c1750;
    --purple-4: #39166c;
    --purple-5: #431f7c;
    --purple-6: #4e2a8b;
    --purple-7: #5e37a4;
    --purple-8: #7645cc;
    --purple-9: #7700e6;
    --purple-10: #6900d2;
    --purple-11: #bfa2ff;
    --purple-12: #e5d9ff;

    --purple-a1: #2f00f211;
    --purple-a2: #6324fe1c;
    --purple-a3: #7728fe44;
    --purple-a4: #7a1ffe62;
    --purple-a5: #8031ff73;
    --purple-a6: #8842ff83;
    --purple-a7: #8e4fff9e;
    --purple-a8: #9153fec9;
    --purple-a9: #8200fee5;
    --purple-a10: #7e00ffcf;
    --purple-a11: #bfa2ff;
    --purple-a12: #e5d9ff;

    --purple-contrast: #fff;
    --purple-surface: #23154580;
    --purple-indicator: #7700e6;
    --purple-track: #7700e6;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        .dark,
        .dark-theme {
            --purple-1: oklch(17.8% 0.0381 295.4);
            --purple-2: oklch(20.9% 0.0477 295.4);
            --purple-3: oklch(26.9% 0.099 295.4);
            --purple-4: oklch(31.1% 0.1376 295.4);
            --purple-5: oklch(34.7% 0.147 295.4);
            --purple-6: oklch(38.9% 0.1528 295.4);
            --purple-7: oklch(44.5% 0.1669 295.4);
            --purple-8: oklch(52.1% 0.198 295.4);
            --purple-9: oklch(49.5% 0.2714 295.4);
            --purple-10: oklch(44.5% 0.2714 295.4);
            --purple-11: oklch(77.9% 0.2119 295.4);
            --purple-12: oklch(91.1% 0.0691 295.4);

            --purple-a1: color(display-p3 0.1333 0 1 / 0.059);
            --purple-a2: color(display-p3 0.3804 0.1451 1 / 0.101);
            --purple-a3: color(display-p3 0.4392 0.1765 0.9961 / 0.253);
            --purple-a4: color(display-p3 0.4549 0.1412 1 / 0.366);
            --purple-a5: color(display-p3 0.4784 0.2118 1 / 0.429);
            --purple-a6: color(display-p3 0.5137 0.2824 1 / 0.492);
            --purple-a7: color(display-p3 0.5451 0.3333 1 / 0.593);
            --purple-a8: color(display-p3 0.5569 0.349 1 / 0.757);
            --purple-a9: color(display-p3 0.4902 0.0745 1 / 0.858);
            --purple-a10: color(display-p3 0.4627 0.0588 1 / 0.778);
            --purple-a11: color(display-p3 0.749 0.6549 1 / 0.975);
            --purple-a12: color(display-p3 0.902 0.8627 1 / 0.988);

            --purple-contrast: #fff;
            --purple-surface: color(display-p3 0.1255 0.0784 0.2588 / 0.5);
            --purple-indicator: oklch(49.5% 0.2714 295.4);
            --purple-track: oklch(49.5% 0.2714 295.4);
        }
    }
}

.dark,
.dark-theme {
    --gray-1: #111113;
    --gray-2: #19191b;
    --gray-3: #222325;
    --gray-4: #292a2e;
    --gray-5: #303136;
    --gray-6: #393a40;
    --gray-7: #46484f;
    --gray-8: #5f606a;
    --gray-9: #6c6e79;
    --gray-10: #797b86;
    --gray-11: #b2b3bd;
    --gray-12: #eeeef0;

    --gray-a1: #1111bb03;
    --gray-a2: #cbcbf90b;
    --gray-a3: #d6e2f916;
    --gray-a4: #d1d9f920;
    --gray-a5: #d7ddfd28;
    --gray-a6: #d9defc33;
    --gray-a7: #dae2fd43;
    --gray-a8: #e0e3fd60;
    --gray-a9: #e0e4fd70;
    --gray-a10: #e3e7fd7e;
    --gray-a11: #eff0feb9;
    --gray-a12: #fdfdffef;

    --gray-contrast: #ffffff;
    --gray-surface: rgba(0, 0, 0, 0.05);
    --gray-indicator: #6c6e79;
    --gray-track: #6c6e79;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        .dark,
        .dark-theme {
            --gray-1: oklch(17.8% 0.0042 277.7);
            --gray-2: oklch(21.5% 0.004 277.7);
            --gray-3: oklch(25.5% 0.0055 277.7);
            --gray-4: oklch(28.4% 0.0075 277.7);
            --gray-5: oklch(31.4% 0.0089 277.7);
            --gray-6: oklch(35% 0.01 277.7);
            --gray-7: oklch(40.2% 0.0121 277.7);
            --gray-8: oklch(49.2% 0.0157 277.7);
            --gray-9: oklch(54% 0.0167 277.7);
            --gray-10: oklch(58.6% 0.0165 277.7);
            --gray-11: oklch(77% 0.0138 277.7);
            --gray-12: oklch(94.9% 0.0026 277.7);

            --gray-a1: color(display-p3 0.0667 0.0667 0.9412 / 0.009);
            --gray-a2: color(display-p3 0.8 0.8 0.9804 / 0.043);
            --gray-a3: color(display-p3 0.851 0.898 0.9882 / 0.085);
            --gray-a4: color(display-p3 0.8392 0.8706 1 / 0.122);
            --gray-a5: color(display-p3 0.8471 0.8745 1 / 0.156);
            --gray-a6: color(display-p3 0.8784 0.898 1 / 0.194);
            --gray-a7: color(display-p3 0.8745 0.9059 0.9961 / 0.257);
            --gray-a8: color(display-p3 0.8941 0.9059 1 / 0.37);
            --gray-a9: color(display-p3 0.8902 0.9098 1 / 0.433);
            --gray-a10: color(display-p3 0.902 0.9176 1 / 0.488);
            --gray-a11: color(display-p3 0.9451 0.949 1 / 0.719);
            --gray-a12: color(display-p3 0.9922 0.9922 1 / 0.937);

            --gray-contrast: #ffffff;
            --gray-surface: color(display-p3 0 0 0 / 5%);
            --gray-indicator: oklch(54% 0.0167 277.7);
            --gray-track: oklch(54% 0.0167 277.7);
        }
    }
}

.dark,
.dark-theme,
:is(.dark, .dark-theme) :where(.radix-themes:not(.light, .light-theme)) {
    --color-background: #111;
}
