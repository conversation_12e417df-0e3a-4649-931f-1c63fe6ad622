import type { AuthError } from "@supabase/supabase-js";
import { Link as RouterLink } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { <PERSON><PERSON>, <PERSON>lex, <PERSON>, Spinner, Text as Typography } from "@/components";
import { Form, Password, Text } from "@/components/form";
import { Notification } from "@/components/notification";
import { links } from "@/utils/links";

export type LoginForm = {
    email: string;
    password: string;
};

const schema: z.ZodSchema<LoginForm> = z.object({
    email: z.email(),
    password: z.string().min(2),
});

type Props = {
    onLogin: (data: LoginForm) => void;
    error: AuthError | null;
    isPending?: boolean;
};

export default function LoginForm({ onLogin, error, isPending }: Props) {
    const { t } = useTranslation();

    return (
        <Form schema={schema} onSubmit={onLogin} defaultValues={{ password: "", email: "" }}>
            <Flex direction="column" gap="4">
                {error && (
                    <Notification type="error">{t("loginForm.logInError", { context: error?.code })}</Notification>
                )}
                <Text<LoginForm>
                    name="email"
                    label={t("loginForm.email")}
                    placeholder={t("loginForm.emailPlaceholder")}
                />
                <Password<LoginForm>
                    name="password"
                    label={t("loginForm.password")}
                    placeholder={t("loginForm.passwordPlaceholder")}
                />
                <Flex justify="end">
                    <Link asChild size={"2"}>
                        <RouterLink to={"/forgot-password"}>{t("loginForm.forgotPassword")}</RouterLink>
                    </Link>
                </Flex>
                <Button type="submit" disabled={isPending}>
                    <Spinner loading={isPending} />
                    {isPending ? t("loggingIn") : t("login")}
                </Button>
                <Flex mt="4" justify="center" gap="1">
                    <Typography size="2">
                        {t("loginForm.dontHaveAccount")}&nbsp;
                        <Link size="2" href={links.contactUs}>
                            {t("loginForm.contactUs")}
                        </Link>
                    </Typography>
                </Flex>
            </Flex>
        </Form>
    );
}
