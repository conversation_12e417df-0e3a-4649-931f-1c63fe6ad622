import { Link as RouterLink } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { Button, Flex, Link } from "@/components";
import { Form, Text } from "@/components/form";
import { Notification } from "@/components/notification";

export type ForgotPasswordFormData = {
    email: string;
};

const schema: z.ZodSchema<ForgotPasswordFormData> = z.object({
    email: z.email(),
});

type Props = {
    onSubmit: (data: ForgotPasswordFormData) => void;
    error?: string;
    isLoading?: boolean;
};

export function ForgotPasswordForm({ onSubmit, error, isLoading }: Props) {
    const { t } = useTranslation();

    return (
        <Form schema={schema} onSubmit={onSubmit} defaultValues={{ email: "" }}>
            <Flex direction="column" gap="4">
                {error && (
                    <div className="mb-4">
                        <Notification type="error">{error}</Notification>
                    </div>
                )}
                <Text<ForgotPasswordFormData>
                    name="email"
                    label={t("forgotPasswordForm.email")}
                    placeholder={t("forgotPasswordForm.emailPlaceholder")}
                    disabled={isLoading}
                    type="email"
                    autoComplete="email"
                />
                <Button type="submit" loading={isLoading}>
                    {t("forgotPasswordForm.sendResetLink")}
                </Button>
                <Flex mt="4" justify="center" gap="1">
                    <Flex justify="end">
                        <Link asChild size={"2"}>
                            <RouterLink to={"/login"}>{t("forgotPasswordForm.backToLogin")}</RouterLink>
                        </Link>
                    </Flex>
                </Flex>
            </Flex>
        </Form>
    );
}
