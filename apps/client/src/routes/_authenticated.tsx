import { createFileRoute, redirect } from "@tanstack/react-router";
import { getUserQueryConfig } from "@/api/auth";

export const Route = createFileRoute("/_authenticated")({
    beforeLoad: async ({ context, location }) => {
        const { queryClient } = context;
        try {
            const user = await queryClient.fetchQuery(getUserQueryConfig());

            if (user) {
                // user found we can stay on authenticated routes
                return;
            }
        } catch {
            throw redirect({ to: "/login", search: { redirect: location.pathname } });
        }
    },
});
