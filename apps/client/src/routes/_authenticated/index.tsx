import { createFile<PERSON>out<PERSON>, <PERSON>, useRouter } from "@tanstack/react-router";
import { useSignOut } from "@/api/auth";
import { Button } from "@/components";

export const Route = createFileRoute("/_authenticated/")({
    component: RouteComponent,
});

function RouteComponent() {
    const { mutateAsync: signOut } = useSignOut();
    const router = useRouter();

    const handleLogout = async () => {
        await signOut();

        await router.navigate({ to: "/login" });
    };

    return (
        <div>
            <div>Hello world!</div>
            <Link to="/settings">Settings</Link>
            <Button onClick={handleLogout}>Logout</Button>
        </div>
    );
}
