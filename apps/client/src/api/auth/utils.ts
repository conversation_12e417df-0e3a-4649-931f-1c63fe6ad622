const getBizzuDomain = () => {
    const { host } = window.location;
    const parts = host.split(".");

    if (import.meta.env.DEV) {
        return host.split(":")[0];
    }

    if (parts.length === 3) {
        const [, domain, topLevelDomain] = parts;

        return `${domain}.${topLevelDomain}`;
    }

    return host;
};

function getCookie(name: string) {
    const cookies = document.cookie.split(";").map((cookie) => cookie.trim());

    return cookies.find((cookie) => cookie.startsWith(`${name}=`))?.split("=")[1];
}

export function getAccessToken() {
    return getCookie("access-token");
}

export function removeAccessToken() {
    document.cookie = `access-token=; domain=.${getBizzuDomain()}; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
}
